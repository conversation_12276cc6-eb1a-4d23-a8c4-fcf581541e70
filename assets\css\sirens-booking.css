/* Sirens Booking Page Styles */
.booking-main {
    min-height: 100vh;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    padding: 2rem 0;
}

.booking-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    align-items: flex-start;
}

.left-column, .right-column {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Modern Calendar Section */
.calendar-section {
    background: rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 0 0.5rem;
}

.calendar-header h2 {
    color: #00ff88;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
}

.calendar-nav-btn {
    background: rgba(0, 255, 136, 0.1);
    border: 1px solid rgba(0, 255, 136, 0.3);
    border-radius: 12px;
    padding: 0.8rem;
    color: #00ff88;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.calendar-nav-btn:hover {
    background: rgba(0, 255, 136, 0.2);
    border-color: #00ff88;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 255, 136, 0.2);
}

.calendar-grid {
    width: 100%;
}

.weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.weekdays div {
    text-align: center;
    font-weight: 600;
    color: #00ff88;
    padding: 0.8rem 0;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.5rem;
}

/* Force gray styling for calendar days with high specificity */
.calendar-section .calendar-grid .days .calendar-day {
    aspect-ratio: 1 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 12px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    font-weight: 500 !important;
    color: #333333 !important;
    background: #808080 !important;
    background-color: #808080 !important;
    background-image: none !important;
    border: 1px solid #666666 !important;
    position: relative !important;
    overflow: hidden !important;
}

.calendar-section .calendar-grid .days .calendar-day:hover {
    background: #999999 !important;
    background-color: #999999 !important;
    background-image: none !important;
    border-color: #777777 !important;
    transform: translateY(-2px) !important;
    color: #222222 !important;
}

.calendar-section .calendar-grid .days .calendar-day.today {
    background: #a0a0a0 !important;
    background-color: #a0a0a0 !important;
    background-image: none !important;
    color: #222222 !important;
    font-weight: 600 !important;
    border-color: #888888 !important;
}

.calendar-section .calendar-grid .days .calendar-day.selected {
    background: linear-gradient(135deg, #00ff88, #00cc6a) !important;
    background-color: #00ff88 !important;
    color: #000 !important;
    font-weight: 700 !important;
    box-shadow: 0 4px 15px rgba(0, 255, 136, 0.4) !important;
    transform: scale(1.05) !important;
    border-color: #00ff88 !important;
}

.calendar-section .calendar-grid .days .calendar-day.today.selected {
    background: linear-gradient(135deg, #00ff88, #00cc6a) !important;
    background-color: #00ff88 !important;
    color: #000 !important;
    font-weight: 700 !important;
    box-shadow: 0 4px 15px rgba(0, 255, 136, 0.4) !important;
}

.calendar-day.other-month {
    color: rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.01);
}

.calendar-day.unavailable {
    color: rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.01);
    cursor: not-allowed;
    position: relative;
}

.calendar-day.unavailable::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 10%;
    right: 10%;
    height: 1px;
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-50%);
}

/* Time Visual Section */
.time-visual-section {
    background: rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.time-visual-section h2 {
    color: #00ff88;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 1.5rem 0;
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
}

.time-visual-container {
    position: relative;
}

.time-slots-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    max-height: 400px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

.time-slot {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.95rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    position: relative;
    overflow: hidden;
}

.time-slot::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent, rgba(0, 255, 136, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.time-slot:hover {
    background: rgba(0, 255, 136, 0.1);
    border-color: rgba(0, 255, 136, 0.3);
    transform: translateY(-2px);
    color: #00ff88;
    box-shadow: 0 4px 15px rgba(0, 255, 136, 0.2);
}

.time-slot:hover::before {
    opacity: 1;
}

.time-slot.selected {
    background: linear-gradient(135deg, #00ff88, #00cc6a);
    color: #000;
    font-weight: 700;
    border-color: #00ff88;
    box-shadow: 0 4px 15px rgba(0, 255, 136, 0.4);
    transform: translateY(-2px);
}

.time-slot.unavailable {
    background: rgba(255, 255, 255, 0.02);
    color: rgba(255, 255, 255, 0.3);
    cursor: not-allowed;
    border-color: rgba(255, 255, 255, 0.05);
}

.time-slot.unavailable:hover {
    transform: none;
    background: rgba(255, 255, 255, 0.02);
    color: rgba(255, 255, 255, 0.3);
    box-shadow: none;
}

/* Custom scrollbar for time slots */
.time-slots-grid::-webkit-scrollbar {
    width: 6px;
}

.time-slots-grid::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
}

.time-slots-grid::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #00ff88, #00cc6a);
    border-radius: 10px;
}

.time-slots-grid::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #00cc6a, #00ff88);
}

/* Pitch Display Section */
.pitch-display-section {
    background: rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.pitch-display-section h3 {
    color: #00ff88;
    font-size: 1.3rem;
    font-weight: 600;
    margin: 0 0 1.5rem 0;
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
}

.pitch-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    align-items: center;
}

.pitch-visual {
    position: relative;
    width: 100%;
    max-width: 400px;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
}

.pitch-visual:hover {
    transform: scale(1.02);
}

.pitch-image {
    width: 100%;
    height: auto;
    display: block;
    border-radius: 16px;
}

.pitch-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
}

.pitch-section {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    background: rgba(0, 0, 0, 0.1);
}

.pitch-section:hover {
    background: rgba(0, 255, 136, 0.2);
}

.pitch-section.selected {
    background: rgba(0, 255, 136, 0.3);
    box-shadow: inset 0 0 0 3px #00ff88;
}

.section-label {
    color: white;
    font-weight: 700;
    font-size: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.pitch-section:hover .section-label,
.pitch-section.selected .section-label {
    opacity: 1;
}

.pitch-options {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
}

.pitch-option-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 0.8rem 1.5rem;
    color: rgba(255, 255, 255, 0.9);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 120px;
    justify-content: center;
}

.pitch-option-btn:hover {
    background: rgba(0, 255, 136, 0.1);
    border-color: rgba(0, 255, 136, 0.3);
    color: #00ff88;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 255, 136, 0.2);
}

.pitch-option-btn.active {
    background: linear-gradient(135deg, #00ff88, #00cc6a);
    color: #000;
    font-weight: 700;
    border-color: #00ff88;
    box-shadow: 0 4px 15px rgba(0, 255, 136, 0.4);
}

.option-icon {
    font-size: 1.1rem;
}

/* Calendar Section */
.calendar-section {
    background: var(--surface-color);
    border-radius: 16px;
    padding: 1.75rem;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.calendar-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
}

.calendar-section h2 {
    color: var(--text-color);
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    font-weight: 600;
    text-align: center;
    position: relative;
    padding-bottom: 0.75rem;
}

.calendar-section h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: 3px;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.75rem;
    text-align: center;
    margin-top: 1.5rem;
    font-size: 0.9rem;
}

/* Day headers (Mon, Tue, etc.) */
.calendar-grid > div:first-child {
    grid-column: 1 / -1;
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.75rem;
    margin-bottom: 0.5rem;
}

.calendar-grid > div:not(:first-child) {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    font-weight: 500;
}

/* Calendar grid layout */
.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.5rem;
    text-align: center;
    margin-bottom: 1rem;
}

/* Day headers (S, M, T, etc.) */
#calendar-days {
    display: contents;
}

#calendar-days > div {
    background: var(--gradient-primary);
    color: #000;
    font-weight: 600;
    font-size: 0.9rem;
    padding: 0.75rem 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin: 0 0 0.5rem 0;
    display: flex;
    align-items: center;
    justify-content: center;
}



/* Booking Form Styling */
.booking-form {
    background: rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.booking-form h2 {
    color: #00ff88;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 1.5rem 0;
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 1rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #00ff88;
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 0 3px rgba(0, 255, 136, 0.1);
}

.form-group input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.submit-booking {
    background: linear-gradient(135deg, #00ff88, #00cc6a);
    color: #000;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 700;
    width: 100%;
    cursor: pointer;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 255, 136, 0.3);
}

.submit-booking:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 255, 136, 0.4);
    filter: brightness(1.1);
}

.submit-booking:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(0, 255, 136, 0.3);
}



/* Calendar navigation */
.calendar-navigation {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    align-items: center;
    margin-bottom: 1.5rem;
    gap: 1rem;
    position: relative;
}

.calendar-title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-size: 1.5rem;
    margin: 0;
    color: var(--text-color);
    font-weight: 600;
    white-space: nowrap;
}

.calendar-month {
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--text-color);
    text-align: center;
    grid-column: 2;
    margin: 0 auto;
}

.calendar-nav-btn {
    justify-self: flex-start;
}

.next-month {
    justify-self: flex-end;
}

.calendar-nav-btn {
    background: rgba(255, 255, 255, 0.05);
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-color);
}

.calendar-nav-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.calendar-nav-btn svg {
    width: 20px;
    height: 20px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .booking-container {
        max-width: 1200px;
        gap: 2rem;
    }

    .calendar-section,
    .time-visual-section,
    .pitch-display-section,
    .booking-form {
        padding: 1.5rem;
    }
}

@media (max-width: 992px) {
    .booking-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        padding: 0 1rem;
    }

    .left-column,
    .right-column {
        gap: 1.5rem;
    }

    .time-slots-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .pitch-options {
        gap: 0.8rem;
    }

    .pitch-option-btn {
        min-width: 100px;
        padding: 0.7rem 1.2rem;
        font-size: 0.85rem;
    }
}

@media (max-width: 768px) {
    .booking-main {
        padding: 1rem 0;
    }

    .calendar-section,
    .time-visual-section,
    .pitch-display-section,
    .booking-form {
        padding: 1.2rem;
    }

    .calendar-header h2,
    .time-visual-section h2,
    .pitch-display-section h3,
    .booking-form h2 {
        font-size: 1.3rem;
    }

    .time-slots-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.8rem;
    }

    .calendar-day {
        font-size: 0.9rem;
    }

    .weekdays div {
        font-size: 0.8rem;
        padding: 0.6rem 0;
    }

    .pitch-options {
        flex-direction: column;
        gap: 0.8rem;
    }

    .pitch-option-btn {
        min-width: auto;
        width: 100%;
    }
}

@media (max-width: 480px) {
    .booking-container {
        padding: 0 0.5rem;
        gap: 1.5rem;
    }

    .calendar-section,
    .time-visual-section,
    .pitch-display-section,
    .booking-form {
        padding: 1rem;
    }

    .calendar-header h2,
    .time-visual-section h2,
    .pitch-display-section h3,
    .booking-form h2 {
        font-size: 1.2rem;
    }

    .calendar-nav-btn {
        padding: 0.6rem;
    }

    .time-slots-grid {
        grid-template-columns: 1fr;
    }

    .time-slot {
        padding: 0.8rem;
        font-size: 0.9rem;
    }

    .calendar-day {
        font-size: 0.85rem;
    }

    .weekdays div {
        font-size: 0.75rem;
        padding: 0.5rem 0;
    }

    .form-group input,
    .form-group select {
        padding: 0.8rem;
        font-size: 0.95rem;
    }

    .submit-booking {
        padding: 0.9rem 1.5rem;
        font-size: 1rem;
    }
}

/* FORCE OVERRIDE - Calendar Day Gray Styling */
div.calendar-day,
.days div.calendar-day,
.calendar-grid .days div.calendar-day,
.calendar-section .calendar-grid .days div.calendar-day {
    background: #808080 !important;
    background-color: #808080 !important;
    background-image: none !important;
    color: #333333 !important;
    border: 1px solid #666666 !important;
}

div.calendar-day:hover,
.days div.calendar-day:hover,
.calendar-grid .days div.calendar-day:hover,
.calendar-section .calendar-grid .days div.calendar-day:hover {
    background: #999999 !important;
    background-color: #999999 !important;
    background-image: none !important;
    color: #222222 !important;
    border-color: #777777 !important;
}

div.calendar-day.today,
.days div.calendar-day.today,
.calendar-grid .days div.calendar-day.today,
.calendar-section .calendar-grid .days div.calendar-day.today {
    background: #a0a0a0 !important;
    background-color: #a0a0a0 !important;
    background-image: none !important;
    color: #222222 !important;
    border-color: #888888 !important;
}

div.calendar-day.selected,
.days div.calendar-day.selected,
.calendar-grid .days div.calendar-day.selected,
.calendar-section .calendar-grid .days div.calendar-day.selected {
    background: linear-gradient(135deg, #00ff88, #00cc6a) !important;
    background-color: #00ff88 !important;
    color: #000 !important;
    font-weight: 700 !important;
    box-shadow: 0 4px 15px rgba(0, 255, 136, 0.4) !important;
    transform: scale(1.05) !important;
    border-color: #00ff88 !important;
}

/* Pitch Container Styles */
.pitch-container {
    margin: 1.5rem 0;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

/* Pitch Visualization Styles */
.pitch-visual {
    border-radius: 24px;
    width: 100%;
    height: 450px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.pitch-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 16px;
    transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.pitch-image:hover {
    transform: scale(1.02);
}



/* Common button styles */
.full-pitch-btn,
.nav-btn {
    background: var(--gradient-primary);
    color: #000;
    border: none;
    padding: 0.8rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    font-size: 1rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    min-width: 140px;
}

.full-pitch-btn {
    margin: 0 auto;
    display: block;
}

.full-pitch-btn:hover,
.nav-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
    filter: brightness(1.05);
}

.full-pitch-btn:active,
.nav-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.pitch-nav {
    display: flex;
    justify-content: space-between;
    margin: 1.5rem 0;
    position: relative;
    width: 100%;
}

/* Navigation buttons */
.nav-btn {
    min-width: 150px;
}

.left-side-btn {
    margin-right: auto;
}

.right-side-btn {
    margin-left: auto;
}

.left-side-btn:hover svg {
    transform: translateX(-3px);
}

.right-side-btn:hover svg {
    transform: translateX(3px);
}

.nav-btn svg {
    width: 18px;
    height: 18px;
    transition: transform 0.2s ease;
}

/* Ensure the pitch container has proper spacing */
.pitch-container {
    margin: 1.5rem 0;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    align-items: center;
    position: relative;
}

.booking-form {
    margin-top: 2rem;
    background: var(--surface-color);
    border-radius: 12px;
    padding: 2rem;
    border: 1px solid var(--border-color);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input, .form-group select {
    width: 100%;
    padding: 0.8rem 1rem;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-color);
}

.submit-booking {
    background: var(--gradient-primary);
    color: #000;
    border: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    width: 100%;
    cursor: pointer;
    margin-top: 1rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .booking-container {
        grid-template-columns: 1fr;
    }
}
