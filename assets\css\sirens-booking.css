/* Sirens Booking Page Styles */
.booking-container {
    display: grid;
    grid-template-columns: 1.2fr 0.8fr;
    gap: 2rem;
    max-width: 1400px;
    margin: 2rem auto;
    padding: 0 2rem;
    align-items: flex-start;
}

/* Time slots container */
.time-slots-container {
    background: #ffffff;
    border-radius: 16px;
    padding: 1.5rem;
    border: 1px solid #e5e7eb;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    width: 100%;
    box-sizing: border-box;
}

.time-slots-container h3 {
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    color: #1f2937;
    font-weight: 600;
}

.time-slots {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    margin-top: 1rem;
}

.time-slot {
    background: #f3f4f6;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 0.75rem 0.5rem;
    text-align: center;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.time-slot:hover {
    background: #e5e7eb;
}

.time-slot.selected {
    background-color: #2563eb;
    color: white;
    border-color: #2563eb;
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .booking-container {
        grid-template-columns: 1fr;
    }
    
    .right-column {
        position: static;
    }
}

@media (max-width: 768px) {
    .calendar-section {
        padding: 1rem;
    }
    
    .time-slots {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 480px) {
    .time-slots {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .calendar-header h2 {
        font-size: 1.1rem;
    }
    
    .day {
        font-size: 0.8rem;
    }
}

/* Calendar Section */
.calendar-section {
    background: var(--surface-color);
    border-radius: 16px;
    padding: 1.75rem;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.calendar-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
}

.calendar-section h2 {
    color: var(--text-color);
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    font-weight: 600;
    text-align: center;
    position: relative;
    padding-bottom: 0.75rem;
}

.calendar-section h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: 3px;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.75rem;
    text-align: center;
    margin-top: 1.5rem;
    font-size: 0.9rem;
}

/* Day headers (Mon, Tue, etc.) */
.calendar-grid > div:first-child {
    grid-column: 1 / -1;
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.75rem;
    margin-bottom: 0.5rem;
}

.calendar-grid > div:not(:first-child) {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    font-weight: 500;
}

/* Calendar grid layout */
.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.5rem;
    text-align: center;
    margin-bottom: 1rem;
}

/* Day headers (S, M, T, etc.) */
#calendar-days {
    display: contents;
}

#calendar-days > div {
    background: var(--gradient-primary);
    color: #000;
    font-weight: 600;
    font-size: 0.9rem;
    padding: 0.75rem 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin: 0 0 0.5rem 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Day cells */
.calendar-day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: transparent;
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;
    font-weight: 500;
    color: var(--text-color);
    font-size: 0.95rem;
}

.calendar-day:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.03);
}

/* Current day styling (when no date is selected) */
.calendar-day.today {
    background: var(--gradient-primary);
    color: #000;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(0, 255, 136, 0.2);
}

/* When a date is selected, style the current day differently */
.calendar-day.today:not(.selected) {
    background: transparent;
    color: var(--primary-color);
    box-shadow: none;
}

/* Selected day styling */
.calendar-day.selected {
    background: var(--gradient-primary);
    color: #000;
    font-weight: 600;
    transform: scale(1);
    box-shadow: 0 2px 8px rgba(0, 255, 136, 0.2);
}

/* Hover effect for non-selected days */
.calendar-day:not(.selected):hover {
    background: rgba(255, 255, 255, 0.05);
}

/* Remove hover effect when a day is selected */
.calendar-day.selected:hover {
    transform: scale(1.05);
}

.calendar-day.other-month {
    opacity: 0.3;
    cursor: default;
    background: transparent;
}

.calendar-day.other-month:hover {
    background: transparent;
    transform: none;
    box-shadow: none;
}

/* Calendar navigation */
.calendar-navigation {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    align-items: center;
    margin-bottom: 1.5rem;
    gap: 1rem;
    position: relative;
}

.calendar-title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-size: 1.5rem;
    margin: 0;
    color: var(--text-color);
    font-weight: 600;
    white-space: nowrap;
}

.calendar-month {
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--text-color);
    text-align: center;
    grid-column: 2;
    margin: 0 auto;
}

.calendar-nav-btn {
    justify-self: flex-start;
}

.next-month {
    justify-self: flex-end;
}

.calendar-nav-btn {
    background: rgba(255, 255, 255, 0.05);
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-color);
}

.calendar-nav-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.calendar-nav-btn svg {
    width: 20px;
    height: 20px;
}

.time-slots {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.8rem;
    margin-top: 1rem;
    max-height: 400px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

.time-slot {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 0.8rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.time-slot:hover {
    background: rgba(0, 255, 136, 0.1);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.time-slot.selected {
    background: var(--gradient-primary);
    color: #000;
    font-weight: 600;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color);
}

/* Scrollbar styling */
.time-slots::-webkit-scrollbar {
    width: 6px;
}

.time-slots::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
}

.time-slots::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

/* Pitch Container Styles */
.pitch-container {
    margin: 1.5rem 0;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

/* Pitch Visualization Styles */
.pitch-visual {
    border-radius: 24px;
    width: 100%;
    height: 450px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.pitch-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 16px;
    transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.pitch-image:hover {
    transform: scale(1.02);
}



/* Common button styles */
.full-pitch-btn,
.nav-btn {
    background: var(--gradient-primary);
    color: #000;
    border: none;
    padding: 0.8rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    font-size: 1rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    min-width: 140px;
}

.full-pitch-btn {
    margin: 0 auto;
    display: block;
}

.full-pitch-btn:hover,
.nav-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
    filter: brightness(1.05);
}

.full-pitch-btn:active,
.nav-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.pitch-nav {
    display: flex;
    justify-content: space-between;
    margin: 1.5rem 0;
    position: relative;
    width: 100%;
}

/* Navigation buttons */
.nav-btn {
    min-width: 150px;
}

.left-side-btn {
    margin-right: auto;
}

.right-side-btn {
    margin-left: auto;
}

.left-side-btn:hover svg {
    transform: translateX(-3px);
}

.right-side-btn:hover svg {
    transform: translateX(3px);
}

.nav-btn svg {
    width: 18px;
    height: 18px;
    transition: transform 0.2s ease;
}

/* Ensure the pitch container has proper spacing */
.pitch-container {
    margin: 1.5rem 0;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    align-items: center;
    position: relative;
}

.booking-form {
    margin-top: 2rem;
    background: var(--surface-color);
    border-radius: 12px;
    padding: 2rem;
    border: 1px solid var(--border-color);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input, .form-group select {
    width: 100%;
    padding: 0.8rem 1rem;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-color);
}

.submit-booking {
    background: var(--gradient-primary);
    color: #000;
    border: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    width: 100%;
    cursor: pointer;
    margin-top: 1rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .booking-container {
        grid-template-columns: 1fr;
    }
}
