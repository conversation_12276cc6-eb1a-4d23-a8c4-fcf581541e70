:root {
    --primary-color: #00FF88;
    --primary-hover: #00CC6F;
    --secondary-color: #00CC6F;
    --accent-color: #00AA5C;
    --background-color: #000000;
    --surface-color: #111111;
    --surface-hover: #1A1A1A;
    --text-color: #FFFFFF;
    --muted-text-color: #BBBBBB;
    --border-color: #333333;
    --success-color: #00FF88;
    --error-color: #FF3366;
    --warning-color: #FFCC00;
    --gradient-primary: linear-gradient(135deg, #00FF88 0%, #00CC6F 100%);
    --gradient-secondary: linear-gradient(135deg, #00CC6F 0%, #00AA5C 100%);
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --shadow-sm: 0 4px 15px rgba(0, 255, 136, 0.15);
    --shadow-md: 0 8px 25px rgba(0, 255, 136, 0.2);
    --shadow-lg: 0 15px 40px rgba(0, 255, 136, 0.25);
    --glow: 0 0 15px rgba(0, 255, 136, 0.5);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.site-header {
    background-color: var(--surface-color);
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.site-header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo a {
    text-decoration: none;
    color: var(--text-color);
    display: flex;
    align-items: center;
}

.logo strong {
    background-color: var(--primary-color);
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 1.2rem;
}

.logo span {
    margin-left: 0.5rem;
    font-weight: 600;
    font-size: 1.2rem;
}

.main-nav ul {
    list-style: none;
    display: flex;
}

.main-nav li {
    margin-left: 2rem;
}

.main-nav a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 600;
    transition: color 0.3s ease;
}

.main-nav a:hover {
    color: var(--primary-color);
}

.auth-buttons .btn {
    margin-left: 1rem;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.85rem 2rem;
    border-radius: 50px;
    font-weight: 700;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    font-family: var(--font-family);
    font-size: 1rem;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    transition: var(--transition);
}

.btn-primary {
    background: var(--gradient-primary);
    color: #000000;
    font-weight: 800;
    box-shadow: var(--shadow-sm);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
    color: #000000;
}

.btn-primary:active {
    transform: translateY(1px);
    box-shadow: var(--shadow-sm);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    padding: calc(0.85rem - 2px) calc(2rem - 2px);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: var(--gradient-primary);
    transition: var(--transition);
    z-index: -1;
}

.btn-secondary:hover {
    border-color: transparent;
    color: #000000;
}

.btn-secondary:hover::before {
    width: 100%;
}

.btn-secondary:active {
    transform: translateY(1px);
}

.btn-lg {
    font-size: 1.1rem;
    padding: 1rem 2rem;
}

/* Hero Section */
.hero {
    text-align: left;
    padding: 6rem 0;
    background: linear-gradient(rgba(26, 26, 26, 0.8), rgba(26, 26, 26, 0.8)), url('https://source.unsplash.com/random/1600x900/?sports,stadium') no-repeat center center/cover;
}

.hero-content {
    max-width: 600px;
    margin: 0 auto 0 10%; /* Keep on left with 10% margin */
    text-align: left;
    padding: 2rem 0;
}

.hero h1 {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    max-width: 100%;
}

.hero p {
    font-size: 1.2rem;
    color: var(--muted-text-color);
    margin-bottom: 2rem;
    max-width: 600px;
}

/* Sports Section */
.sports-section {
    padding: 4rem 0;
}

.sports-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
}

.sports-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin: 2rem auto;
    max-width: 1000px;
    padding: 0 1rem;
    width: 100%;
}

.sport-card {
    background-color: var(--surface-color);
    padding: 1.5rem;
    border-radius: 12px;
    text-align: center;
    border: 1px solid var(--border-color);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-width: 0; /* Prevents flex items from overflowing */
    width: 100%;
    box-sizing: border-box;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    .sports-grid {
        grid-template-columns: repeat(2, 1fr);
        max-width: 700px;
    }
}

@media (max-width: 640px) {
    .sports-grid {
        grid-template-columns: 1fr;
        max-width: 400px;
    }
}

.sport-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.sport-icon {
    font-size: 3.5rem;
    margin: 0 auto;
    width: 90px;
    height: 90px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 255, 136, 0.1);
    border-radius: 50%;
    border: 2px solid var(--primary-color);
    transition: var(--transition);
}

.sport-card:hover .sport-icon {
    transform: scale(1.1) rotate(5deg);
    background: rgba(0, 255, 136, 0.2);
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.2);
}

.sport-card h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.sport-card p {
    color: var(--muted-text-color);
    margin: 1rem 0 2rem;
    line-height: 1.7;
    flex-grow: 1;
}

.sport-card .btn {
    margin-top: auto;
    align-self: center;
    width: 80%;
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
    font-weight: 700;
    letter-spacing: 0.5px;
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    transition: all 0.3s ease;
}

.sport-card .btn:hover {
    background: var(--gradient-primary);
    color: #000;
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.sport-card .btn:active {
    transform: translateY(0);
}

/* Featured Venues */
.featured-venues {
    background-color: var(--surface-color);
    padding: 4rem 0;
}

.featured-venues h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
}

.venue-card {
    background-color: #3a3a3a;
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

/* Footer */
.site-footer {
    background-color: var(--surface-color);
    padding: 2rem 0;
    margin-top: 4rem;
    border-top: 1px solid var(--border-color);
    text-align: center;
}

.footer-links a {
    color: var(--muted-text-color);
    text-decoration: none;
    margin: 0 1rem;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--primary-color);
}

/* Page Title Section */
.page-title {
    background-color: var(--surface-color);
    padding: 3rem 0;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
}

.page-title h1 {
    font-size: 2.8rem;
}

.page-title p {
    font-size: 1.1rem;
    color: var(--muted-text-color);
    margin-top: 0.5rem;
}

/* Booking Flow */
.booking-flow-section {
    padding: 4rem 0;
}

/* Progress Bar */
.progress-bar {
    display: flex;
    justify-content: space-between;
    margin-bottom: 3rem;
    position: relative;
    width: 80%;
    margin-left: auto;
    margin-right: auto;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, 
        var(--primary-color) 0%, 
        var(--primary-color) 33.33%, 
        var(--border-color) 33.33%, 
        var(--border-color) 100%);
    background-size: 300% 100%;
    background-position: 100% 0;
    transform: translateY(-50%);
    z-index: -1;
    border-radius: 2px;
    transition: background-position 0.5s ease;
}

.progress-step {
    background-color: var(--background-color);
    padding: 0 1rem;
    color: var(--muted-text-color);
    font-weight: 600;
    position: relative;
}

.progress-step::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    background-color: var(--background-color);
    z-index: -1;
}

.progress-step.active {
    color: var(--primary-color);
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
}

.progress-step.active::before {
    border-color: var(--primary-color);
    box-shadow: 0 0 15px rgba(0, 255, 136, 0.3);
    animation: pulse 2s infinite;
}

.progress-step.completed {
    color: var(--primary-color);
}

.progress-step.completed::before {
    border-color: var(--primary-color);
    background: var(--gradient-primary);
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(0, 255, 136, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(0, 255, 136, 0); }
    100% { box-shadow: 0 0 0 0 rgba(0, 255, 136, 0); }
}


/* Selection Container */
.selection-container {
    text-align: center;
    margin-bottom: 4rem;
}

.selection-container h2 {
    font-size: 2.2rem;
    margin-bottom: 0.5rem;
}

.selection-container p {
    color: var(--muted-text-color);
    margin-bottom: 2.5rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

/* FAQ Section */
.faq-section {
    padding: 4rem 0;
    background-color: var(--background-color);
}

.faq-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: var(--text-color);
    position: relative;
    display: inline-block;
    left: 50%;
    transform: translateX(-50%);
}

.faq-section h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: var(--gradient-primary);
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2.5rem;
    margin: 3rem 0;
}

.faq-card {
    background-color: var(--surface-color);
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    border: 1px solid var(--border-color);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
}

.faq-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    border-color: var(--primary-color);
}

.faq-icon-circle {
    font-size: 2.5rem;
    margin: 0 auto 1.5rem;
    width: 90px;
    height: 90px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 255, 136, 0.1);
    border-radius: 50%;
    border: 2px solid var(--primary-color);
    transition: var(--transition);
    color: var(--primary-color);
}

.faq-card:hover .faq-icon-circle {
    transform: scale(1.1) rotate(5deg);
    background: rgba(0, 255, 136, 0.2);
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.2);
}

.faq-card h3 {
    color: var(--text-color);
    font-size: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.faq-card p {
    color: var(--muted-text-color);
    margin: 1rem 0 2rem;
    line-height: 1.7;
    flex-grow: 1;
}

.faq-card .btn {
    margin-top: auto;
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
    font-weight: 700;
    letter-spacing: 0.5px;
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    transition: all 0.3s ease;
    border-radius: 50px;
    text-decoration: none;
    display: inline-block;
}

.faq-card .btn:hover {
    background: var(--gradient-primary);
    color: #000;
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.faq-card .btn:active {
    transform: translateY(0);
}

/* Sports Grid & Cards */
.sports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.sport-card-link {
    text-decoration: none;
}

.sport-card {
    background: var(--surface-color);
    padding: 2.5rem 1.75rem;
    border-radius: 16px;
    border: 1px solid var(--border-color);
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    z-index: 1;
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    height: 100%;
    box-shadow: var(--shadow-sm);
}

.sport-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--gradient-primary);
    transition: var(--transition);
}

.sport-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-lg);
    background: var(--surface-hover);
    border-color: rgba(0, 255, 136, 0.3);
}

.sport-card h3 {
    font-size: 1.8rem;
    margin: 1.5rem 0 1rem;
    background: linear-gradient(135deg, #FFFFFF 0%, var(--primary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    position: relative;
    display: inline-block;
    margin-left: auto;
    margin-right: auto;
}

.sport-card h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 50px;
    height: 3px;
    background: var(--gradient-primary);
    margin: 0 auto;
    right: 0;
}

.sport-card h3 {
    font-size: 1.5rem;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.sport-card p {
    color: var(--muted-text-color);
    font-size: 1rem;
    margin: 0;
}

/* Venues Grid */
.venues-grid {
    display: grid;
    grid-template-columns: repeat(3, 300px);
    justify-content: center;
    gap: 1.5rem;
    margin: 0 auto;
    padding: 0 1rem;
    width: 100%;
    max-width: 1000px;
}

.venue-card-link {
    text-decoration: none;
    display: block;
    height: 100%;
}

.venue-card {
    background: var(--surface-color);
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid var(--border-color);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
    min-width: 0;
    box-sizing: border-box;
}

.venue-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--gradient-primary);
    transition: var(--transition);
}

.venue-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    background: var(--surface-hover);
    border-color: rgba(0, 255, 136, 0.3);
}

/* Responsive adjustments */
@media (max-width: 1000px) {
    .venues-grid {
        grid-template-columns: repeat(2, 300px);
        max-width: 700px;
    }
}

@media (max-width: 700px) {
    .venues-grid {
        grid-template-columns: 300px;
        max-width: 100%;
    }
}

.venue-card h4 {
    font-size: 1.4rem;
    margin-bottom: 0.5rem;
    color: white;
    position: relative;
    display: inline-block;
}

.venue-card h4::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 50%;
    height: 2px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
}

.venue-card:hover h4::after {
    transform: scaleX(1);
    transform-origin: left;
}

.venue-card p {
    color: var(--muted-text-color);
    font-size: 0.9rem;
    margin: 0.5rem 0 0;
    transition: color 0.3s ease;
}

.venue-card:hover p {
    color: var(--text-color);
}

.venue-card h4 {
    font-size: 1.3rem;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.venue-card p {
    color: var(--muted-text-color);
    font-size: 0.9rem;
    margin: 0;
}


/* Rules Container */
.rules-container {
    background-color: var(--surface-color);
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid var(--border-color);
    max-width: 800px;
    margin: 0 auto;
}

.rules-container h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.rules-container ul {
    list-style: none;
    padding: 0;
}

.rules-container li {
    color: var(--muted-text-color);
    padding-left: 1.5rem;
    position: relative;
    margin-bottom: 0.75rem;
}

.rules-container li::before {
    content: '✔';
    color: var(--primary-color);
    position: absolute;
    left: 0;
}

/* Mobile Navigation */
.mobile-nav-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 1.5rem;
    cursor: pointer;
}

@media (max-width: 992px) {
    .booking-interface .container {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .main-nav, .auth-buttons {
        display: none;
    }

    .mobile-nav-toggle {
        display: block;
    }

    .site-header .container {
        justify-content: space-between;
    }

    .hero h1 {
        font-size: 2.5rem;
    }
}
