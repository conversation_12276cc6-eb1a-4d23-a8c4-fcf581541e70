// Sirens Booking Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the booking system
    initializeCalendar();
    initializeTimeSlots();
    initializePitchSelection();
    initializeBookingForm();
});

// Calendar functionality
let currentDate = new Date();
let selectedDate = null;
let selectedTimeSlot = null;
let selectedPitchOption = 'full';

function initializeCalendar() {
    updateCalendarDisplay();

    // Add event listeners for navigation buttons
    document.querySelector('.prev-month').addEventListener('click', () => {
        currentDate.setMonth(currentDate.getMonth() - 1);
        updateCalendarDisplay();
    });

    document.querySelector('.next-month').addEventListener('click', () => {
        currentDate.setMonth(currentDate.getMonth() + 1);
        updateCalendarDisplay();
    });
}

function updateCalendarDisplay() {
    const monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
    ];

    // Update month header
    document.getElementById('current-month').textContent =
        `${monthNames[currentDate.getMonth()]} ${currentDate.getFullYear()}`;

    // Generate calendar days
    generateCalendarDays();
}

function generateCalendarDays() {
    const daysContainer = document.getElementById('calendar-days');
    daysContainer.innerHTML = '';

    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    // Get first day of month and number of days
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    // Add empty cells for days before month starts
    for (let i = 0; i < startingDayOfWeek; i++) {
        const emptyDay = document.createElement('div');
        emptyDay.className = 'calendar-day other-month';
        daysContainer.appendChild(emptyDay);
    }

    // Add days of the month
    const today = new Date();
    const todayDateOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());

    for (let day = 1; day <= daysInMonth; day++) {
        const dayElement = document.createElement('div');
        dayElement.className = 'calendar-day';
        dayElement.textContent = day;

        const dayDate = new Date(year, month, day);
        const dayDateOnly = new Date(year, month, day);

        // Mark today
        if (dayDateOnly.getTime() === todayDateOnly.getTime()) {
            dayElement.classList.add('today');
        }

        // Mark past dates with black styling
        if (dayDateOnly < todayDateOnly) {
            dayElement.classList.add('past');
            // No click event for past dates
        } else {
            // All future dates (including today) are available - add click event
            dayElement.addEventListener('click', () => selectDate(dayDate, dayElement));

            // For demo purposes, we're not marking any dates as booked
            // In the future, you can add: dayElement.classList.add('booked'); for booked dates
        }

        daysContainer.appendChild(dayElement);
    }
}

function selectDate(date, element) {
    // Remove previous selection
    document.querySelectorAll('.calendar-day.selected').forEach(day => {
        day.classList.remove('selected');
    });

    // Add selection to clicked date
    element.classList.add('selected');
    selectedDate = date;

    // Update time slots for selected date
    updateTimeSlots();
}

// Time slots functionality
function initializeTimeSlots() {
    updateTimeSlots();
}

function updateTimeSlots() {
    const timeSlotsContainer = document.querySelector('.time-slots-grid');
    timeSlotsContainer.innerHTML = '';

    // Generate time slots from 6 AM to 11 PM
    const timeSlots = [];
    for (let hour = 6; hour <= 23; hour++) {
        for (let minute = 0; minute < 60; minute += 60) {
            const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
            timeSlots.push(time);
        }
    }

    timeSlots.forEach(time => {
        const slotElement = document.createElement('div');
        slotElement.className = 'time-slot';
        slotElement.textContent = time;

        // All time slots are available (no backend yet)
        // In the future, you can check against booked times and add 'unavailable' class
        slotElement.addEventListener('click', () => selectTimeSlot(time, slotElement));

        timeSlotsContainer.appendChild(slotElement);
    });
}

function selectTimeSlot(time, element) {
    // Remove previous selection
    document.querySelectorAll('.time-slot.selected').forEach(slot => {
        slot.classList.remove('selected');
    });

    // Add selection to clicked slot
    element.classList.add('selected');
    selectedTimeSlot = time;
}

// Pitch selection functionality
function initializePitchSelection() {
    const pitchOptions = document.querySelectorAll('.pitch-option-btn');
    const pitchSections = document.querySelectorAll('.pitch-section');

    // Add click events to pitch option buttons
    pitchOptions.forEach(button => {
        button.addEventListener('click', () => {
            const option = button.dataset.option;
            selectPitchOption(option, button);
        });
    });

    // Add click events to pitch sections
    pitchSections.forEach(section => {
        section.addEventListener('click', () => {
            const sectionType = section.dataset.section;
            if (sectionType) {
                const button = document.querySelector(`[data-option="${sectionType}"]`);
                selectPitchOption(sectionType, button);
            }
        });
    });

    // Initialize with full pitch selected (highlight entire image)
    const pitchVisual = document.querySelector('.pitch-visual');
    pitchVisual.classList.add('full-pitch-selected');
}

function selectPitchOption(option, buttonElement) {
    // Remove previous selections
    document.querySelectorAll('.pitch-option-btn.active').forEach(btn => {
        btn.classList.remove('active');
    });

    document.querySelectorAll('.pitch-section.selected').forEach(section => {
        section.classList.remove('selected');
    });

    // Remove full pitch highlighting
    const pitchVisual = document.querySelector('.pitch-visual');
    pitchVisual.classList.remove('full-pitch-selected');

    // Add new selection
    buttonElement.classList.add('active');
    selectedPitchOption = option;

    // Highlight corresponding pitch section or full pitch
    if (option === 'full') {
        // Highlight entire pitch
        pitchVisual.classList.add('full-pitch-selected');
    } else {
        // Highlight specific section
        const section = document.querySelector(`[data-section="${option}"]`);
        if (section) {
            section.classList.add('selected');
        }
    }
}

// Booking form functionality
function initializeBookingForm() {
    const form = document.getElementById('booking-form');

    form.addEventListener('submit', (e) => {
        e.preventDefault();
        handleBookingSubmission();
    });
}

function handleBookingSubmission() {
    // Validate selections
    if (!selectedDate) {
        alert('Please select a date');
        return;
    }

    if (!selectedTimeSlot) {
        alert('Please select a time slot');
        return;
    }

    // Get form data
    const formData = new FormData(document.getElementById('booking-form'));
    const bookingData = {
        date: selectedDate.toDateString(),
        time: selectedTimeSlot,
        pitchOption: selectedPitchOption,
        fullName: formData.get('fullName'),
        email: formData.get('email'),
        phone: formData.get('phone'),
        players: formData.get('players')
    };

    // Show confirmation
    showBookingConfirmation(bookingData);
}

function showBookingConfirmation(data) {
    const confirmationMessage = `
        Booking Confirmation:

        Date: ${data.date}
        Time: ${data.time}
        Pitch: ${data.pitchOption === 'full' ? 'Full Pitch' : data.pitchOption + ' Half'}
        Players: ${data.players}

        Name: ${data.fullName}
        Email: ${data.email}
        Phone: ${data.phone}

        Your booking has been submitted successfully!
    `;

    alert(confirmationMessage);

    // Reset form and selections
    resetBookingForm();
}

function resetBookingForm() {
    // Reset form
    document.getElementById('booking-form').reset();

    // Reset selections
    document.querySelectorAll('.calendar-day.selected').forEach(day => {
        day.classList.remove('selected');
    });

    document.querySelectorAll('.time-slot.selected').forEach(slot => {
        slot.classList.remove('selected');
    });

    // Reset to full pitch
    document.querySelectorAll('.pitch-option-btn.active').forEach(btn => {
        btn.classList.remove('active');
    });

    document.querySelector('[data-option="full"]').classList.add('active');

    selectedDate = null;
    selectedTimeSlot = null;
    selectedPitchOption = 'full';
}