<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book Sirens FC - Sports Malta</title>
    <link rel="stylesheet" href="/assets/css/style.css">
    <link rel="stylesheet" href="/assets/css/sirens-booking.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
   
</head>
<body>
    <header class="site-header">
        <div class="container">
            <div class="logo">
                <a href="index.html">
                    <strong>SM</strong>
                    <span>Sports Malta</span>
                </a>
            </div>
            <nav class="main-nav">
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="booking.html">Booking</a></li>
                    <li><a href="contact.html">Contact</a></li>
                </ul>
            </nav>
            <div class="auth-buttons">
                <button class="btn btn-secondary">Log In</button>
            </div>
            <button class="mobile-nav-toggle">&#9776;</button>
        </div>
    </header>

    <main class="booking-main">
        <div class="booking-container">
            <div class="left-column">
                <!-- Modern Calendar Section -->
                <div class="calendar-section">
                    <div class="calendar-header">
                        <button class="calendar-nav-btn prev-month">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                        <h2 id="current-month">August 2025</h2>
                        <button class="calendar-nav-btn next-month">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                    </div>
                    <div class="calendar-grid">
                        <div class="weekdays">
                            <div>Sun</div>
                            <div>Mon</div>
                            <div>Tue</div>
                            <div>Wed</div>
                            <div>Thu</div>
                            <div>Fri</div>
                            <div>Sat</div>
                        </div>
                        <div class="days" id="calendar-days"></div>
                    </div>
                </div>

                <!-- User Input Form under Calendar -->
                <div class="booking-form">
                    <h2>Your Details</h2>
                    <form id="booking-form">
                        <div class="form-group">
                            <label>Full Name</label>
                            <input type="text" name="fullName" required>
                        </div>
                        <div class="form-group">
                            <label>Email</label>
                            <input type="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label>Phone</label>
                            <input type="tel" name="phone" required>
                        </div>
                        <div class="form-group">
                            <label>Number of Players</label>
                            <select name="players" required>
                                <option value="">Select players</option>
                                <option value="5">5 vs 5</option>
                                <option value="7">7 vs 7</option>
                                <option value="11">11 vs 11 (Full Pitch)</option>
                            </select>
                        </div>
                        <button type="submit" class="submit-booking">Book Now</button>
                    </form>
                </div>
            </div>

            <div class="right-column">
                <!-- Time Visual Section -->
                <div class="time-visual-section">
                    <h2>Available Time Slots</h2>
                    <div class="time-visual-container">
                        <div class="time-slots-grid">
                            <!-- Time slots will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Football Pitch Image Section -->
                <div class="pitch-display-section">
                    <h3>Pitch Layout</h3>
                    <div class="pitch-container">
                        <div class="pitch-visual">
                            <img src="/images/Lines_On_A_Football_Pitch.jpg" alt="Football Pitch with Lines" class="pitch-image">
                            <div class="pitch-overlay">
                                <div class="pitch-section left-section" data-section="left">
                                    <span class="section-label">Left Half</span>
                                </div>
                                <div class="pitch-section right-section" data-section="right">
                                    <span class="section-label">Right Half</span>
                                </div>
                            </div>
                        </div>

                        <div class="pitch-options">
                            <button class="pitch-option-btn active" data-option="full">
                                <span class="option-icon">⚽</span>
                                Full Pitch
                            </button>
                            <button class="pitch-option-btn" data-option="left">
                                <span class="option-icon">🥅</span>
                                Left Half
                            </button>
                            <button class="pitch-option-btn" data-option="right">
                                <span class="option-icon">🥅</span>
                                Right Half
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

  <script src="/assets/js/sirens-booking.js"></script>
</body>
</html>
