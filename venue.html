<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Venue Booking - Sports Malta</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body>

    <header class="site-header">
        <div class="container">
            <div class="logo">
                <a href="index.html">
                    <strong>SM</strong>
                    <span>Sports Malta</span>
                </a>
            </div>
            <nav class="main-nav">
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="booking.html">Booking</a></li>
                    <li><a href="contact.html">Contact</a></li>
                </ul>
            </nav>
            <div class="auth-buttons">
                <button class="btn btn-secondary">Log In</button>
            </div>
            <button class="mobile-nav-toggle">&#9776;</button>
        </div>
    </header>

    <main>
        <section class="booking-flow-section">
            <div class="container">
                <!-- Progress Bar -->
                <div class="progress-bar">
                    <div class="progress-step completed">1. Choose Sport</div>
                    <div class="progress-step active">2. Select Venue</div>
                    <div class="progress-step">3. Confirm Booking</div>
                </div>

                <!-- Venue Selection -->
                <div class="selection-container">
                    <h2>Select a Venue</h2>
                    <p>Showing venues for your selected sport. Click a venue to see availability.</p>
                    <div class="sports-grid" id="venues-container">
                        <!-- Football Venues -->
                        <a href="sirens-booking.html" class="sport-card-link" data-sport="football">
                            <div class="sport-card" id="sirens">
                                <div class="sport-icon">🏟️</div>
                                <h3>Sirens FC</h3>
                                <p>St. Paul's Bay • 5/7-a-side pitches • Floodlit • Changing rooms • Parking available</p>
                                <div class="venue-rating">★★★★☆ 4.2 (128 reviews)</div>
                                <button type="button" class="btn">View Availability</button>
                            </div>
                        </a>
                        <a href="#" class="sport-card-link" data-sport="football">
                            <div class="sport-card" id="luxol">
                                <div class="sport-icon">🏟️</div>
                                <h3>Luxol St. Andrew's</h3>
                                <p>Pembroke • Full-size pitch • Clubhouse • Floodlit • Professional coaching</p>
                                <div class="venue-rating">★★★★★ 4.7 (95 reviews)</div>
                                <button type="button" class="btn">View Availability</button>
                            </div>
                        </a>
                        <a href="#" class="sport-card-link" data-sport="football">
                            <div class="sport-card" id="melita">
                                <div class="sport-icon">🏟️</div>
                                <h3>Melita FC</h3>
                                <p>St. Julian's • 5/7-a-side • Artificial turf • Night lighting • Equipment rental</p>
                                <div class="venue-rating">★★★★☆ 4.3 (87 reviews)</div>
                                <button type="button" class="btn">View Availability</button>
                            </div>
                        </a>
                        
                        <!-- Tennis Venues -->
                        <a href="#" class="sport-card-link" data-sport="tennis">
                            <div class="sport-card" id="marsa">
                                <div class="sport-icon">🎾</div>
                                <h3>Marsa Sports Club</h3>
                                <p>Marsa • Tennis courts • Clubhouse • Coaching • Equipment rental</p>
                                <div class="venue-rating">★★★★☆ 4.1 (76 reviews)</div>
                                <button type="button" class="btn">View Availability</button>
                            </div>
                        </a>
                        <a href="#" class="sport-card-link" data-sport="tennis">
                            <div class="sport-card" id="pembroke">
                                <div class="sport-icon">🎾</div>
                                <h3>Pembroke Tennis Club</h3>
                                <p>Pembroke • Clay & hard courts • Night lighting • Pro shop • Coaching</p>
                                <div class="venue-rating">★★★★★ 4.8 (112 reviews)</div>
                                <button type="button" class="btn">View Availability</button>
                            </div>
                        </a>
                        
                        <!-- Padel Venues -->
                        <a href="#" class="sport-card-link" data-sport="padel">
                            <div class="sport-card" id="padel-malta">
                                <div class="sport-icon">🎾</div>
                                <h3>Padel Malta</h3>
                                <p>Multiple locations • Glass courts • Equipment included • Group lessons • Leagues</p>
                                <div class="venue-rating">★★★★☆ 4.6 (203 reviews)</div>
                                <button type="button" class="btn">View Availability</button>
                            </div>
                        </a>
                        <a href="#" class="sport-card-link" data-sport="padel">
                            <div class="sport-card" id="padel-park">
                                <div class="sport-icon">🎾</div>
                                <h3>Padel Park</h3>
                                <p>Coming Soon • St. Julian's • 4 Glass courts • Clubhouse • Pro shop • Coaching</p>
                                <div class="venue-rating">Coming Soon</div>
                                <button type="button" class="btn" disabled>Coming Soon</button>
                            </div>
                        </a>
                        <a href="#" class="sport-card-link" data-sport="padel">
                            <div class="sport-card" id="smash-padel">
                                <div class="sport-icon">🎾</div>
                                <h3>Smash Padel Club</h3>
                                <p>Coming Soon • Sliema • 6 Indoor courts • Bar & Lounge • Tournaments • Kids programs</p>
                                <div class="venue-rating">Coming Soon</div>
                                <button type="button" class="btn" disabled>Coming Soon</button>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="site-footer">
        <div class="container">
            <p>&copy; 2024 Sports Malta. All rights reserved.</p>
            <div class="footer-links">
                <a href="#">Contact</a>
                <a href="#">About Us</a>
                <a href="#">Terms of Service</a>
            </div>
        </div>
    </footer>

    <script src="/assets/js/script.js"></script>
    <script>
        // Function to filter venues by sport type
        function filterVenues(sportType) {
            const venues = document.querySelectorAll('.sport-card-link');
            const container = document.getElementById('venues-container');
            
            // Show all venues if no sport type is specified
            if (!sportType) {
                venues.forEach(venue => {
                    venue.style.display = '';
                });
                return;
            }
            
            // Filter venues based on the selected sport
            venues.forEach(venue => {
                if (venue.dataset.sport === sportType) {
                    venue.style.display = '';
                } else {
                    venue.style.display = 'none';
                }
            });
            
            // Update the heading to show the filtered sport
            const heading = document.querySelector('.selection-container h2');
            if (heading) {
                heading.textContent = `${sportType.charAt(0).toUpperCase() + sportType.slice(1)} Venues`;
            }
        }
        
        // Check URL for sport parameter on page load
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const sportType = urlParams.get('sport');
            if (sportType) {
                filterVenues(sportType);
            }
        });
    </script>
</body>
</html>
